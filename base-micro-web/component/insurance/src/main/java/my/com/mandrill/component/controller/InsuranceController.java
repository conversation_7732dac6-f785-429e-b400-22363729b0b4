package my.com.mandrill.component.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import io.swagger.v3.oas.annotations.Hidden;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import my.com.mandrill.component.config.MapStructConverter;
import my.com.mandrill.component.constant.NetWorthMainType;
import my.com.mandrill.component.constant.NetWorthSource;
import my.com.mandrill.component.domain.Insurance;
import my.com.mandrill.component.dto.model.InsuranceDTO;
import my.com.mandrill.component.dto.model.InsuranceDTOV2;
import my.com.mandrill.component.dto.request.*;
import my.com.mandrill.component.service.InsuranceIntegrationService;
import my.com.mandrill.component.service.InsuranceService;
import my.com.mandrill.component.service.InsuranceValidationService;
import my.com.mandrill.component.service.TypeService;
import my.com.mandrill.utilities.feign.client.PropertyFeignClient;
import my.com.mandrill.utilities.feign.dto.DetailedNetWorthDTO;
import my.com.mandrill.utilities.feign.dto.NetWorthDTO;
import my.com.mandrill.utilities.feign.dto.PropertyDTO;
import my.com.mandrill.utilities.feign.dto.VaultLinkDTO;
import my.com.mandrill.utilities.general.constant.InsuranceTypeEnum;
import my.com.mandrill.utilities.general.util.SecurityUtil;
import org.springframework.data.domain.Sort;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;

// TODO Andy refactor and cleanup after next release
@Slf4j
@RestController
@RequiredArgsConstructor
public class InsuranceController {

	private final InsuranceIntegrationService insuranceIntegrationService;

	private final InsuranceService insuranceService;

	private final InsuranceValidationService insuranceValidationService;

	private final ObjectMapper objectMapper;

	private final TypeService typeService;

	private final PropertyFeignClient propertyFeignClient;

	@PostMapping("v2/insurances")
	@PreAuthorize("hasAuthority(@authorityPermission.USER_UPDATE)")
	public ResponseEntity<InsuranceDTOV2> createV2(
			@Valid @RequestBody CreateInsuranceRequestV2 createInsuranceRequest) {
		Insurance insurance = objectMapper.convertValue(createInsuranceRequest, Insurance.class);

		String userId = SecurityUtil.currentUserId();

		insuranceValidationService.validateCreate(insurance, userId);

		Insurance result = insuranceIntegrationService.create(insurance);

		insuranceIntegrationService.sendDashboardActivity(result.getCreatedDate());

		insuranceIntegrationService.publishNetWorthTransactionEvent(userId, NetWorthMainType.INSURANCE_COVERAGE,
				NetWorthSource.INSURANCE, result.getType(), new BigDecimal(result.getCoverageLimit()));

		insuranceIntegrationService.publishNetWorthTransactionEvent(userId, NetWorthMainType.RECURRING_REPAYMENT,
				NetWorthSource.INSURANCE, result.getType(), new BigDecimal(result.getPremium()));

		return ResponseEntity.ok(MapStructConverter.MAPPER.toInsuranceDTOV2(result));
	}

	@Hidden
	@PostMapping("insurances/integration")
	@PreAuthorize("hasAuthority(@authorityPermission.USER_UPDATE)")
	public ResponseEntity<InsuranceDTO> integration(
			@Valid @RequestBody CreateInsuranceIntegrationRequest createInsuranceRequest) {
		Insurance insurance = MapStructConverter.MAPPER.toInsurance(createInsuranceRequest);

		String userId = SecurityUtil.currentUserId();
		Insurance validatedInsurance = insuranceValidationService.validateIntegrationsCreateUpdate(insurance, userId);
		Insurance result = insuranceService.save(validatedInsurance);

		return ResponseEntity.ok(MapStructConverter.MAPPER.toInsuranceDTO(result));
	}

	@GetMapping("insurances")
	@PreAuthorize("hasAuthority(@authorityPermission.USER_READ)")
	public ResponseEntity<List<InsuranceDTO>> findAll(Sort sort) {
		String userId = SecurityUtil.currentUserId();
		List<Insurance> insurances = insuranceService.findAll(userId, sort);
		List<InsuranceDTO> results = typeService
				.processInsuranceType(insurances.stream().map(MapStructConverter.MAPPER::toInsuranceDTO).toList());

		return ResponseEntity.ok(results);
	}

	@GetMapping("insurances/{id}")
	@PreAuthorize("hasAuthority(@authorityPermission.USER_READ)")
	public ResponseEntity<InsuranceDTO> findById(@PathVariable String id) {

		String userId = SecurityUtil.currentUserId();
		Insurance insurance = insuranceService.findById(id, userId);
		InsuranceDTO insuranceDTO = MapStructConverter.MAPPER.toInsuranceDTO(insurance);
		typeService.processInsuranceType(Collections.singletonList(insuranceDTO));

		return ResponseEntity.ok(insuranceDTO);
	}

	@GetMapping("insurances/{type}/{entityId}")
	@PreAuthorize("hasAuthority(@authorityPermission.USER_READ)")
	public ResponseEntity<InsuranceDTO> findByEntityNameAndEntityId(@PathVariable InsuranceTypeEnum type,
			@PathVariable String entityId) {

		String userId = SecurityUtil.currentUserId();
		Insurance result = insuranceService.findByTypeAndEntityIdAndUser(type, entityId, userId);

		return ResponseEntity.ok(MapStructConverter.MAPPER.toInsuranceDTO(result));
	}

	@PutMapping("v2/insurances/{id}")
	@PreAuthorize("hasAuthority(@authorityPermission.USER_UPDATE)")
	public ResponseEntity<InsuranceDTOV2> updateV2(@Valid @RequestBody UpdateInsuranceRequestV2 updateInsuranceRequest,
			@PathVariable String id) {
		Insurance insurance = MapStructConverter.MAPPER.toInsuranceV2(updateInsuranceRequest);
		insurance.setId(id);

		String userId = SecurityUtil.currentUserId();
		Insurance existingInsurance = insuranceService.findById(insurance.getId(), userId);
		insuranceValidationService.validateUpdate(insurance, existingInsurance);
		Insurance result = insuranceIntegrationService.update(existingInsurance);

		return ResponseEntity.ok(MapStructConverter.MAPPER.toInsuranceDTOV2(result));
	}

	@PutMapping("insurances/{id}/attach-entity")
	@ResponseStatus(HttpStatus.NO_CONTENT)
	@PreAuthorize("hasAuthority(@authorityPermission.USER_UPDATE)")
	public void attachEntity(@Valid @RequestBody ObjectRequest request, @PathVariable String id) {
		String userId = SecurityUtil.currentUserId();
		insuranceService.attachEntity(request, id, userId);
	}

	@PutMapping("insurances/{id}/detach-entity")
	@ResponseStatus(HttpStatus.NO_CONTENT)
	@PreAuthorize("hasAuthority(@authorityPermission.USER_UPDATE)")
	public void detachEntity(@PathVariable String id) {
		String userId = SecurityUtil.currentUserId();
		Insurance insurance = insuranceService.findById(id, userId);
		insurance.setEntityId(null);

		insuranceService.save(insurance);
	}

	@Hidden
	@PutMapping("insurances/integration/{id}")
	@PreAuthorize("hasAuthority(@authorityPermission.USER_UPDATE)")
	public ResponseEntity<InsuranceDTO> integrationUpdate(
			@Valid @RequestBody UpdateInsuranceIntegrationRequest updateInsuranceRequest) {
		Insurance insurance = MapStructConverter.MAPPER.toInsurance(updateInsuranceRequest);

		String userId = SecurityUtil.currentUserId();
		Insurance validatedInsurance = insuranceValidationService.validateIntegrationsCreateUpdate(insurance, userId);
		Insurance result = insuranceService.save(validatedInsurance);

		return ResponseEntity.ok(MapStructConverter.MAPPER.toInsuranceDTO(result));
	}

	@DeleteMapping("v2/insurances/{id}")
	@ResponseStatus(HttpStatus.NO_CONTENT)
	@PreAuthorize("hasAuthority(@authorityPermission.USER_UPDATE)")
	public void deleteV2(@PathVariable String id) {
		String userId = SecurityUtil.currentUserId();
		insuranceIntegrationService.delete(id, userId);
	}

	@GetMapping("insurances/count")
	@PreAuthorize("hasAuthority(@authorityPermission.USER_READ)")
	public ResponseEntity<Long> count() {
		String userId = SecurityUtil.currentUserId();
		return ResponseEntity.ok(insuranceService.count(userId));
	}

	@GetMapping("insurances/properties-without-insurance")
	@PreAuthorize("hasAuthority(@authorityPermission.USER_READ)")
	public ResponseEntity<List<PropertyDTO>> findPropertiesWithoutInsurance() {
		String userId = SecurityUtil.currentUserId();
		List<PropertyDTO> result = propertyFeignClient.getProperties();
		List<String> entityIdList = insuranceService.findAllEntityIdByUserIdAndTypeAndEntityIdNotNull(userId,
				InsuranceTypeEnum.PROPERTY);

		return ResponseEntity.ok(result.stream().filter(propertyDTO -> !entityIdList.contains(propertyDTO.getId()))
				.sorted(Comparator.comparing(PropertyDTO::getCreatedDate).reversed()).toList());
	}

	@Hidden
	@ResponseStatus(HttpStatus.NO_CONTENT)
	@PutMapping("insurances/vault/link/{id}")
	@PreAuthorize("hasAuthority(@authorityPermission.USER_UPDATE)")
	public void linkVault(@RequestBody VaultLinkDTO vaultLinkDTO, @PathVariable String id) {

		String userId = SecurityUtil.currentUserId();
		Insurance existingInsurance = insuranceService.findById(id, userId);
		insuranceService.linkVault(vaultLinkDTO, existingInsurance);

	}

	@Hidden
	@GetMapping("insurances/vault/linked/{attachmentGroupId}")
	@PreAuthorize("hasAuthority(@authorityPermission.USER_READ)")
	public ResponseEntity<InsuranceDTO> findLinkedVault(@PathVariable String attachmentGroupId) {
		String userId = SecurityUtil.currentUserId();
		Insurance insurance = insuranceService.findByAttachmentGroupId(attachmentGroupId, userId);
		return ResponseEntity.ok(MapStructConverter.MAPPER.toInsuranceDTO(insurance));
	}

	@Hidden
	@GetMapping("insurances/vault/unlinked")
	@PreAuthorize("hasAuthority(@authorityPermission.USER_READ)")
	public ResponseEntity<List<InsuranceDTO>> getVehicleWithAttachmentGroupIdNull() {
		String userId = SecurityUtil.currentUserId();
		List<Insurance> insurances = insuranceService.findByAttachmentGroupIdNull(userId);
		return ResponseEntity.ok().body(insurances.stream().map(MapStructConverter.MAPPER::toInsuranceDTO).toList());
	}

	@Hidden
	@GetMapping("insurances/vault/is-linked/{attachmentGroupId}")
	@PreAuthorize("hasAuthority(@authorityPermission.USER_READ)")
	public ResponseEntity<Boolean> existsByUserIdAndAttachmentGroupId(@PathVariable String attachmentGroupId) {
		String userId = SecurityUtil.currentUserId();
		return ResponseEntity.ok(insuranceService.existsByUserIdAndAttachmentGroupId(userId, attachmentGroupId));
	}

	@Hidden
	@GetMapping("insurances/integration/net-worth")
	public ResponseEntity<NetWorthDTO> calculateNetWorth(
			@RequestParam(required = false, defaultValue = "false") boolean isRevampVersion) {
		String userId = SecurityUtil.currentUserId();
		NetWorthDTO netWorthDTO = insuranceService.calculateNetWorthByUserId(userId, isRevampVersion);
		return ResponseEntity.ok(netWorthDTO);
	}

	@Hidden
	@GetMapping("insurances/integration/net-worth-extended")
	public List<DetailedNetWorthDTO> calculateNetWorthExtended() {
		return insuranceService.extendedNetWorthByUserId(SecurityUtil.currentUserId());
	}

	@GetMapping("insurances/available-resources")
	public List<InsuranceDTO> getAvailableResources(@RequestParam InsuranceTypeEnum insuranceType) {
		String userId = SecurityUtil.currentUserId();
		List<Insurance> insurances = insuranceService.findDetached(userId, insuranceType);
		return insurances.stream().map(MapStructConverter.MAPPER::toInsuranceDTO).toList();
	}

	@GetMapping("insurances/integration/linked-entity")
	public List<String> getLinkedEntityIds(@RequestParam InsuranceTypeEnum insuranceType) {
		String userId = SecurityUtil.currentUserId();
		return insuranceService.findAllEntityIdByUserIdAndTypeAndEntityIdNotNull(userId, insuranceType);
	}

}
