package my.com.mandrill.component.service.impl;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import my.com.mandrill.component.constant.NetWorthMainType;
import my.com.mandrill.component.constant.NetWorthSource;
import my.com.mandrill.component.constant.NetWorthType;
import my.com.mandrill.component.domain.Insurance;
import my.com.mandrill.component.service.InsuranceIntegrationService;
import my.com.mandrill.component.service.InsuranceService;
import my.com.mandrill.utilities.feign.dto.request.UpdateUserNetWorthTransactionRequest;
import my.com.mandrill.utilities.feign.dto.request.UpdateVehicleInsuranceRenewalRequest;
import my.com.mandrill.utilities.general.constant.DashboardCategory;
import my.com.mandrill.utilities.general.constant.DashboardType;
import my.com.mandrill.utilities.general.constant.InsuranceTypeEnum;
import my.com.mandrill.utilities.general.constant.KafkaTopic;
import my.com.mandrill.utilities.general.dto.model.DashboardActivityMessage;
import my.com.mandrill.utilities.general.dto.model.InsuranceRenewalUpdatedData;
import my.com.mandrill.utilities.general.service.DashboardTriggerService;
import my.com.mandrill.utilities.general.service.KafkaSender;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDate;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

@Slf4j
@Service
@RequiredArgsConstructor
public class InsuranceIntegrationServiceImpl implements InsuranceIntegrationService {

	private final InsuranceService insuranceService;

	private final ObjectMapper objectMapper;

	private final DashboardTriggerService dashboardTriggerService;

	private final KafkaTemplate<String, String> kafkaTemplate;

	private final KafkaSender kafkaSender;

	@Override
	public Insurance create(Insurance insurance) {
		return insuranceService.save(insurance);
	}

	@Override
	public Insurance update(Insurance insurance) {
		Insurance existing = insuranceService.findById(insurance.getId(), insurance.getUserId());
		Insurance persisted = insuranceService.save(insurance);

		if (Objects.nonNull(persisted.getRenewalDate())
				&& persisted.getRenewalDate().isAfter(existing.getRenewalDate())) {
			this.insuranceRenewalDateUpdatedEvent(persisted.getId(), persisted.getRenewalDate());
		}
		return persisted;
	}

	@Override
	public void delete(String id, String userId) {
		Insurance insurance = insuranceService.findById(id, userId);
		insuranceService.delete(insurance);
	}

	@Override
	public void sendDashboardActivity(Instant createdDate) {
		DashboardActivityMessage dto = DashboardActivityMessage.builder().category(DashboardCategory.MODULE_RECORDS)
				.type(DashboardType.INSURANCES).value(1L).createdDate(createdDate).build();

		dashboardTriggerService.send(dto);
	}

	@Override
	public void detachByEntityIdAndType(String entityId, InsuranceTypeEnum type, String userId) {
		Optional<Insurance> insuranceOptional = insuranceService.findByTypeAndEntityIdAndUserOptional(type, entityId,
				userId);
		if (insuranceOptional.isPresent()) {
			Insurance insurance = insuranceOptional.get();
			insurance.setEntityId(null);
			insuranceService.save(insurance);
		}
	}

	@Override
	public void updateInsuranceRoadTaxFinology(UpdateVehicleInsuranceRenewalRequest request)
			throws JsonProcessingException {
		Insurance insurance = insuranceService.updateInsuranceRoadTaxFinology(request);

		this.insuranceRenewalDateUpdatedEvent(insurance.getId(), insurance.getRenewalDate());
	}

	@Override
	public void publishNetWorthTransactionEvent(String userId, NetWorthMainType mainType, NetWorthSource source,
			String insuranceType, BigDecimal amount) {

		// todo: move this into enum class and create method get by code, replace in other
		// occurences
		Map<String, NetWorthType> netWorthTypeMap = Map.ofEntries(
				Map.entry(InsuranceTypeEnum.MEDICAL_HEALTH.getCode(),
						NetWorthType.MEDICAL_HEALTH_INSURANCE_MONTHLY_REPAYMENT),
				Map.entry(InsuranceTypeEnum.LIFE.getCode(), NetWorthType.LIFE_INSURANCE_MONTHLY_REPAYMENT),
				Map.entry(InsuranceTypeEnum.PROPERTY.getCode(), NetWorthType.PROPERTY_INSURANCE_MONTHLY_REPAYMENT),
				Map.entry(InsuranceTypeEnum.VEHICLE.getCode(), NetWorthType.VEHICLE_INSURANCE_MONTHLY_REPAYMENT),
				Map.entry(InsuranceTypeEnum.TRAVEL.getCode(), NetWorthType.TRAVEL_INSURANCE_MONTHLY_REPAYMENT),
				Map.entry(InsuranceTypeEnum.PERSONAL_ACCIDENT.getCode(),
						NetWorthType.PERSONAL_ACCIDENT_INSURANCE_MONTHLY_REPAYMENT),
				Map.entry(InsuranceTypeEnum.OTHERS.getCode(), NetWorthType.OTHERS_INSURANCE_MONTHLY_REPAYMENT));

		UpdateUserNetWorthTransactionRequest request = UpdateUserNetWorthTransactionRequest.builder().userId(userId)
				.mainType(mainType).subType(netWorthTypeMap.get(insuranceType)).amount(amount).source(source).build();

		kafkaSender.safeSendWrapped(KafkaTopic.UPDATE_USER_NET_WORTH_TRANSACTION_TOPIC, userId, request);
	}

	private void insuranceRenewalDateUpdatedEvent(String insuranceId, LocalDate newRenewal) {
		try {
			kafkaTemplate.send(KafkaTopic.INSURANCE_RENEWAL_DATE_UPDATED_EVENT, objectMapper.writeValueAsString(
					InsuranceRenewalUpdatedData.builder().renewalDate(newRenewal).insuranceId(insuranceId).build()));
		}
		catch (Exception e) {
			log.error("error when trigger event insurance renewal date updated: ", e);
		}
	}

}
