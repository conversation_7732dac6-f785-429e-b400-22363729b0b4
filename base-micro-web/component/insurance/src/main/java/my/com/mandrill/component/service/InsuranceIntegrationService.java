package my.com.mandrill.component.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import my.com.mandrill.component.constant.NetWorthMainType;
import my.com.mandrill.component.constant.NetWorthSource;
import my.com.mandrill.component.domain.Insurance;
import my.com.mandrill.utilities.feign.dto.request.UpdateVehicleInsuranceRenewalRequest;
import my.com.mandrill.utilities.general.constant.InsuranceTypeEnum;

import java.math.BigDecimal;
import java.time.Instant;

public interface InsuranceIntegrationService {

	// TODO Andy refactor and cleanup after next release

	Insurance create(Insurance insurance);

	Insurance update(Insurance insurance);

	void delete(String id, String userId);

	void sendDashboardActivity(Instant createdDate);

	void detachByEntityIdAndType(String entityId, InsuranceTypeEnum type, String userId);

	void updateInsuranceRoadTaxFinology(UpdateVehicleInsuranceRenewalRequest request) throws JsonProcessingException;

	void publishNetWorthTransactionEvent(String userId, NetWorthMainType mainType, NetWorthSource source,
			String insuranceType, BigDecimal amount);

}
