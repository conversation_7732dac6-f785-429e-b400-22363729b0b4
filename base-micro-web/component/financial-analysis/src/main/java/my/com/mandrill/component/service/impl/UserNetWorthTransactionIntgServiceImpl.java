package my.com.mandrill.component.service.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import my.com.mandrill.component.config.MapStructConverter;
import my.com.mandrill.component.constant.NetWorthMainType;
import my.com.mandrill.component.domain.primary.UserNetWorthTransaction;
import my.com.mandrill.component.service.UserNetWorthTransactionIntgService;
import my.com.mandrill.component.service.UserNetWorthTransactionService;
import my.com.mandrill.utilities.feign.dto.request.UpdateUserNetWorthTransactionRequest;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;

@Service
@Slf4j
@RequiredArgsConstructor
public class UserNetWorthTransactionIntgServiceImpl implements UserNetWorthTransactionIntgService {

	private final UserNetWorthTransactionService userNetWorthTransactionService;

	@Override
	public void processUserNetWorthTransaction(UpdateUserNetWorthTransactionRequest request) {

		UserNetWorthTransaction userNetWorthTransaction = MapStructConverter.MAPPER.toUserNetWorthTransaction(request);

		// get current total net worth for the user
		BigDecimal currentTotalNetWorth = userNetWorthTransactionService
				.findLatestTotalNetWorthByUserId(request.getUserId());

		if (!NetWorthMainType.ASSETS.equals(request.getMainType())
				&& !NetWorthMainType.LIABILITIES.equals(request.getMainType())) {
			// net worth only change for assets and liabilities,
			// hence total net worth remains the same
			userNetWorthTransaction.setTotalNetWorth(currentTotalNetWorth);
			userNetWorthTransactionService.save(userNetWorthTransaction);
			return;
		}

		// get current net worth amount for this specific subtype
		BigDecimal currentAmount = userNetWorthTransactionService
				.findLatestNetWorthAmountByUserIdAndSubType(request.getUserId(), request.getSubType());

		// calculate the difference
		BigDecimal difference = request.getAmount().subtract(currentAmount);

		// add the difference to the existing total net worth
		BigDecimal newTotalNetWorth = NetWorthMainType.LIABILITIES.equals(request.getMainType())
				? currentTotalNetWorth.subtract(difference) : currentTotalNetWorth.add(difference);

		// always create new record as transaction
		userNetWorthTransaction.setTotalNetWorth(newTotalNetWorth);
		userNetWorthTransactionService.save(userNetWorthTransaction);
	}

}
