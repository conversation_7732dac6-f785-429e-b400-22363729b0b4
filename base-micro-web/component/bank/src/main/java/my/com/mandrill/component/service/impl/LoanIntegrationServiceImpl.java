package my.com.mandrill.component.service.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import my.com.mandrill.component.config.MapStructConverter;
import my.com.mandrill.component.constant.NetWorthMainType;
import my.com.mandrill.component.constant.NetWorthSource;
import my.com.mandrill.component.constant.NetWorthType;
import my.com.mandrill.component.domain.Bank;
import my.com.mandrill.component.domain.Loan;
import my.com.mandrill.component.dto.request.NewLoanRequest;
import my.com.mandrill.component.dto.response.LoanAggregateResponse;
import my.com.mandrill.component.service.*;
import my.com.mandrill.utilities.feign.client.CommonFeignClient;
import my.com.mandrill.utilities.feign.client.NotificationFeignClient;
import my.com.mandrill.utilities.feign.client.PropertyFeignClient;
import my.com.mandrill.utilities.feign.client.VehicleFeignClient;
import my.com.mandrill.utilities.feign.dto.DetailedNetWorthDTO;
import my.com.mandrill.utilities.feign.dto.PropertyDTO;
import my.com.mandrill.utilities.feign.dto.UserJourneyRequest;
import my.com.mandrill.utilities.feign.dto.request.UpdateUserNetWorthTransactionRequest;
import my.com.mandrill.utilities.general.constant.*;
import my.com.mandrill.utilities.general.dto.model.DashboardActivityMessage;
import my.com.mandrill.utilities.general.dto.model.VehicleDTO;
import my.com.mandrill.utilities.general.dto.response.UserJourneyResponse;
import my.com.mandrill.utilities.general.service.DashboardTriggerService;
import my.com.mandrill.utilities.general.service.KafkaSender;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.Instant;
import java.util.*;

@Slf4j
@RequiredArgsConstructor
@Service
public class LoanIntegrationServiceImpl implements LoanIntegrationService {

	private static final int PAGE_SIZE = 100;

	private final LoanService loanService;

	private final NotificationFeignClient notificationFeignClient;

	private final BankService bankService;

	private final CommonFeignClient commonFeignClient;

	private final DashboardTriggerService dashboardTriggerService;

	private final PropertyFeignClient propertyFeignClient;

	private final VehicleFeignClient vehicleFeignClient;

	private final PopulateService populateService;

	private final BankLoanCalculationService bankLoanCalculationService;

	private final KafkaSender kafkaSender;

	@Override
	public Loan save(Loan loan) {
		bankService.save(loan.getBank());
		return loanService.save(loan);
	}

	private void deleteLoanInstallmentReminder(String id) {
		notificationFeignClient.deleteByReminderTypeAndDataId(ReminderType.LOAN_INSTALMENT, id);
	}

	@Override
	public void delete(String id, String userId) {
		loanService.findByIdAndUserId(id, userId).ifPresent(loan -> {
			Bank bank = loan.getBank();

			loanService.delete(loan);
			bankService.deleteByIdAndUserIdAndBankDetailsEmptyAndLoansEmpty(bank.getId(), userId);
			deleteLoanInstallmentReminder(id);
			commonFeignClient.deleteUserJourneyByEntityName(EntityName.LOAN, id);
		});

	}

	@Override
	public void deleteV2(String id, String userId) {
		loanService.findByIdAndUserId(id, userId).ifPresent(loan -> {
			Bank bank = loan.getBank();

			loanService.delete(loan);
			bankService.deleteByIdAndUserIdAndBankDetailsEmptyAndLoansEmpty(bank.getId(), userId);
		});
	}

	@Override
	public void completeUserJourney(String loanId, String userJourneyId) {
		if (StringUtils.isNotBlank(userJourneyId)) {
			UserJourneyResponse response = commonFeignClient.getUserJourneyById(userJourneyId);
			UserJourneyRequest userJourneyRequest = UserJourneyRequest.builder().id(userJourneyId)
					.entityName(EntityName.LOAN).entityId(loanId).build();
			commonFeignClient.createUserJourney(response.getJourneyStep().getName(), userJourneyRequest);
		}
	}

	@Override
	public void sendDashboardActivity(Instant createdDate) {
		DashboardActivityMessage dto = DashboardActivityMessage.builder().category(DashboardCategory.MODULE_RECORDS)
				.type(DashboardType.LOANS).value(1L).createdDate(createdDate).build();

		dashboardTriggerService.send(dto);
	}

	@Override
	public LoanAggregateResponse countTotalLoan(String userId) {
		BigDecimal homeLoan = loanService.countTotalByType(userId, LoanTypeEnum.HOME_LOANS);
		BigDecimal autoLoan = loanService.countTotalByType(userId, LoanTypeEnum.AUTO_LOANS);
		BigDecimal personalLoan = loanService.countTotalByType(userId, LoanTypeEnum.PERSONAL_LOANS);
		BigDecimal educationLoan = loanService.countTotalByType(userId, LoanTypeEnum.EDUCATION_LOANS);
		BigDecimal islamicLoan = loanService.countTotalByType(userId, LoanTypeEnum.ISLAMIC_FINANCING);
		BigDecimal otherLoan = loanService.countTotalByType(userId, LoanTypeEnum.OTHER_LOAN);
		return new LoanAggregateResponse(homeLoan, autoLoan, personalLoan, educationLoan, islamicLoan, otherLoan);
	}

	@Override
	public List<Loan> findLoanForKYLL(String userId) {
		return loanService.findLoanForKYLL(userId);
	}

	@Override
	public List<String> findAttachedLoan(String userId, EntityName entityName) {
		List<Loan> loans = loanService.findAttachedLoan(userId, entityName);
		return loans.stream().map(Loan::getEntityId).toList();
	}

	@Override
	public List<Loan> findAllV2(String userId, Sort sort) {
		List<Loan> loans = loanService.findAll(userId, sort);
		this.mapEntityDetails(loans);
		return loans;
	}

	@Override
	public void mapEntityDetails(List<Loan> loans) {
		// get all property or vehicle to map the labels
		List<PropertyDTO> properties = propertyFeignClient.getPropertiesForIntegration();
		HashMap<String, PropertyDTO> propertyMap = new HashMap<>();
		if (properties != null) {
			for (PropertyDTO property : properties) {
				propertyMap.put(property.getId(), property);
			}
		}

		List<VehicleDTO> vehicles = vehicleFeignClient.findAllForIntegration();
		HashMap<String, VehicleDTO> vehicleMap = new HashMap<>();
		if (vehicles != null) {
			for (VehicleDTO vehicle : vehicles) {
				vehicleMap.put(vehicle.getId(), vehicle);
			}
		}

		for (Loan loan : loans) {
			if (null != loan.getEntityName() && EntityName.PROPERTY.equals(loan.getEntityName())) {
				if (propertyMap.containsKey(loan.getEntityId())) {
					PropertyDTO property = propertyMap.get(loan.getEntityId());
					String label = property.getPropertyType().getName();
					if (property.getPropertySubType() != null) {
						label = property.getPropertyType().getName().concat(" - ")
								.concat(property.getPropertySubType().getName());
					}
					loan.setLabel(label);
					loan.setPurchaseValue(property.getPurchaseValue());
				}
			}
			else if (null != loan.getEntityName() && EntityName.VEHICLE.equals(loan.getEntityName())) {
				if (vehicleMap.containsKey(loan.getEntityId())) {
					VehicleDTO vehicle = vehicleMap.get(loan.getEntityId());
					if (Boolean.TRUE.equals(vehicle.getIsFinology())) {
						loan.setPurchaseValue(vehicle.getVixMarketValue());
						loan.setLabel(vehicle.getVixVehicleModel());
					}
					else {
						loan.setPurchaseValue(vehicle.getAverageMarketValue());
						if (vehicle.getBrandName() != null && vehicle.getModelName() != null) {
							loan.setLabel(vehicle.getBrandName().concat(" ").concat(vehicle.getModelName()));
						}
					}
				}
			}
		}
	}

	@Override
	public void detachByEntityIdAndEntityName(String entityId, EntityName entityName, String userId) {
		Optional<Loan> loanOptional = loanService.findOptionalByEntityNameAndEntityId(entityName, entityId, userId);
		if (loanOptional.isPresent()) {
			Loan loan = loanOptional.get();
			loan.setEntityId(null);
			loan.setEntityName(null);
			loanService.save(loan);
		}
	}

	@Override
	public BigDecimal calculateNewLoanEndingBalance(NewLoanRequest newLoanRequest) {
		Loan newLoan = MapStructConverter.MAPPER.toLoan(newLoanRequest);
		return loanService.calculateLoanEndingBalance(newLoan);
	}

	@Override
	public Loan findByIdV2(String id, String userId) {
		Loan result = loanService.findById(id, userId);
		if (result.getEntityName() != null && result.getEntityName().equals(EntityName.PROPERTY)) {
			PropertyDTO property = loanService.withDetachedCheck(result.getId(),
					() -> propertyFeignClient.findById(result.getEntityId()));

			if (Objects.nonNull(property)) {
				String label = property.getPropertyType().getName().concat(" - ")
						.concat(property.getPropertySubType().getName());
				result.setLabel(label);
				result.setPurchaseValue(property.getPurchaseValue());
			}
		}
		else if (result.getEntityName() != null && result.getEntityName().equals(EntityName.VEHICLE)) {
			VehicleDTO vehicle = loanService.withDetachedCheck(result.getId(),
					() -> vehicleFeignClient.findVehicleById(result.getEntityId()));

			if (Objects.nonNull(vehicle)) {
				if (Boolean.TRUE.equals(vehicle.getIsFinology())) {
					result.setLabel(vehicle.getVixVehicleModel());
					result.setPurchaseValue(vehicle.getVixMarketValue());
				}
				else {
					result.setPurchaseValue(vehicle.getAverageMarketValue());
					if (vehicle.getBrandName() != null && vehicle.getModelName() != null) {
						result.setLabel(vehicle.getBrandName().concat(" ").concat(vehicle.getModelName()));
					}
				}
			}
		}
		return result;
	}

	@Override
	public void getAllProcessedOnGoingLoans() {
		int page = 0;
		long totalFetched = 0;
		long totalProcessed = 0;
		Page<Loan> loanPage;

		do {
			Pageable pageable = PageRequest.of(page, PAGE_SIZE, Sort.by(Sort.Direction.ASC, "createdDate"));
			loanPage = loanService.findAllOnGoingLoans(pageable);

			if (page == 0) {
				totalFetched = loanPage.getTotalElements();
			}

			List<Loan> toSave = new ArrayList<>();

			for (Loan loan : loanPage.getContent()) {
				Loan processedLoan = populateService.populateLoanPaymentStatus(loan);
				if (processedLoan != null)
					toSave.add(processedLoan);
			}

			if (!toSave.isEmpty()) {
				loanService.saveAll(toSave);
				totalProcessed += toSave.size();
			}

			page++;
		}
		while (!loanPage.isLast());

		log.info("Monthly Loan Calculation fetched total: {}, processed (saved): {}", totalFetched, totalProcessed);
	}

	@Override
	public void publishNetWorthTransactionEvent(String userId, NetWorthMainType mainType, NetWorthType subType,
			NetWorthSource source) {

		bankLoanCalculationService.calculateDetailedNetWorthByUserId(userId).stream()
				.filter(data -> DetailedNetWorthDTO.ValueType.LIABILITY.equals(data.getValueType())
						|| DetailedNetWorthDTO.ValueType.RECURRING_REPAYMENT.equals(data.getValueType()))
				.forEach(data -> {

					NetWorthMainType netWorthMainType = DetailedNetWorthDTO.ValueType.LIABILITY.equals(
							data.getValueType()) ? NetWorthMainType.LIABILITIES : NetWorthMainType.RECURRING_REPAYMENT;

					UpdateUserNetWorthTransactionRequest request = UpdateUserNetWorthTransactionRequest.builder()
							.userId(userId).mainType(netWorthMainType).subType(data.getNetWorthType())
							.amount(data.getValue()).source(source).build();

					kafkaSender.safeSendWrapped(KafkaTopic.UPDATE_USER_NET_WORTH_TRANSACTION_TOPIC, userId, request);
				});

	}

}
