package my.com.mandrill.component.service.impl;

import jakarta.persistence.EntityNotFoundException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import my.com.mandrill.component.config.MapStructConverter;
import my.com.mandrill.component.constant.NetWorthMainType;
import my.com.mandrill.component.constant.NetWorthSource;
import my.com.mandrill.component.constant.NetWorthType;
import my.com.mandrill.component.domain.Income;
import my.com.mandrill.component.domain.User;
import my.com.mandrill.component.dto.model.IncomeDTO;
import my.com.mandrill.component.dto.model.UserIncome;
import my.com.mandrill.component.dto.request.ReminderIntegrationRequest;
import my.com.mandrill.component.service.IncomeIntegrationService;
import my.com.mandrill.component.service.IncomeService;
import my.com.mandrill.utilities.feign.dto.ObjectRequest;
import my.com.mandrill.utilities.feign.dto.ReminderRequest;
import my.com.mandrill.utilities.feign.dto.ReminderResponse;
import my.com.mandrill.utilities.feign.dto.request.UpdateUserNetWorthTransactionRequest;
import my.com.mandrill.utilities.feign.service.ProxyFeignClient;
import my.com.mandrill.utilities.general.constant.*;
import my.com.mandrill.utilities.general.service.KafkaSender;
import my.com.mandrill.utilities.general.util.NumberUtil;
import my.com.mandrill.utilities.general.util.SecurityUtil;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.List;
import java.util.Map;
import java.util.TreeMap;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class IncomeIntegrationServiceImpl implements IncomeIntegrationService {

	public static final String B_1_LESS_THAN_RM_2_499 = "B1 - Less than RM2,499";

	public static final String B_2_RM_2_500_TO_RM_3_169 = "B2 - RM2,500 to RM3,169";

	public static final String B_3_RM_3_170_TO_RM_3_969 = "B3 - RM3,170 to RM3,969";

	public static final String B_4_RM_3_970_TO_RM_4_849 = "B4 - RM3,970 to RM4,849";

	public static final String M_1_TO_RM_4_850_TO_RM_5879 = "M1 - RM4,850 to RM5,879";

	public static final String M_2_RM_5_880_TO_RM_7_099 = "M2 - RM5,880 to RM7,099";

	public static final String M_3_RM_7_100_TO_RM_8_699 = "M3 - RM7,100 to RM8,699";

	public static final String M_4_RM_8_700_TO_RM_10_959 = "M4 - RM8,700 to RM10,959";

	public static final String T_1_RM_10_960_T_0_RM_15_039 = "T1 - RM10,960 t0 RM15,039";

	public static final String T_2_MORE_THAN_RM_15_039 = "T2 - More than RM15,039";

	private final IncomeService incomeService;

	private final ProxyFeignClient proxyFeignClient;

	private final KafkaSender kafkaSender;

	@Override
	public void processReminder(Income income, Boolean isReminder,
			ReminderIntegrationRequest reminderIntegrationRequest) {
		ReminderType reminderType = ReminderType.INCOME;
		if (Boolean.TRUE.equals(isReminder) && reminderIntegrationRequest != null) {
			ReminderRequest reminderRequest = MapStructConverter.MAPPER.toReminderRequest(reminderIntegrationRequest);
			reminderRequest.setDeliveryType(DeliveryType.PUSH);
			reminderRequest.setReminderFrequency(ReminderFrequency.MONTHLY);

			ObjectRequest data = new ObjectRequest();
			data.setId(income.getId());
			reminderRequest.setData(data);
			reminderRequest.setReminderType(reminderType);
			reminderRequest.setIncome(MapStructConverter.MAPPER.toIncomeDTO(income));

			ReminderResponse response;
			try {
				response = proxyFeignClient.getNotificationFeignClient().findByReminderTypeAndDataId(reminderType,
						income.getId());
				proxyFeignClient.getNotificationFeignClient().update(reminderRequest, response.getId());
			}
			catch (EntityNotFoundException e) {
				proxyFeignClient.getNotificationFeignClient().integration(reminderRequest);
			}
		}
		else if (Boolean.FALSE.equals(isReminder)) {
			deleteReminder(income);
		}
	}

	@Override
	public void deleteReminder(Income income) {
		proxyFeignClient.getNotificationFeignClient().deleteByReminderTypeAndDataId(ReminderType.INCOME,
				income.getId());
	}

	@Override
	public void delete(String id, User user) {
		incomeService.findOptionalByIdAndUser(id, user).ifPresent(income -> {
			incomeService.delete(income);
			deleteReminder(income);
		});
	}

	// TODO: Refactor this to reduce cognitive complexity
	@Override
	public Map<String, Map<String, Long>> countIncomeGroup(LocalDate startDate, String timeZone) {
		final Instant time = startDate.atStartOfDay(ZoneId.of(timeZone)).toInstant();
		log.info("Processing for startDate: {}", time);

		List<UserIncome> userIncomes = incomeService
				.findByUserDeletedFalseAndLoginTypeAndCreatedDateGreaterThanEqual(LoginTypeEnum.USER, time);
		log.info("Processing {} userIncomes", userIncomes.size());

		// Group By User and count all their associated incomes
		Map<String, BigDecimal> map = userIncomes.parallelStream().collect(Collectors.groupingBy(UserIncome::getUserId,
				Collectors.reducing(BigDecimal.ZERO, UserIncome::getIncome, BigDecimal::add)));
		log.info("Processing {} unique users", map.size());

		Map<String, Map<String, Long>> result = new TreeMap<>();
		Map<String, Long> b40 = new TreeMap<>();
		b40.put(B_1_LESS_THAN_RM_2_499, 0L);
		b40.put(B_2_RM_2_500_TO_RM_3_169, 0L);
		b40.put(B_3_RM_3_170_TO_RM_3_969, 0L);
		b40.put(B_4_RM_3_970_TO_RM_4_849, 0L);

		Map<String, Long> m40 = new TreeMap<>();
		m40.put(M_1_TO_RM_4_850_TO_RM_5879, 0L);
		m40.put(M_2_RM_5_880_TO_RM_7_099, 0L);
		m40.put(M_3_RM_7_100_TO_RM_8_699, 0L);
		m40.put(M_4_RM_8_700_TO_RM_10_959, 0L);

		Map<String, Long> t20 = new TreeMap<>();
		t20.put(T_1_RM_10_960_T_0_RM_15_039, 0L);
		t20.put(T_2_MORE_THAN_RM_15_039, 0L);

		map.forEach((key, value) -> {
			// b40
			if (NumberUtil.isBetween(value, new BigDecimal(0), new BigDecimal(2499))) {
				b40.put(B_1_LESS_THAN_RM_2_499, b40.getOrDefault(B_1_LESS_THAN_RM_2_499, 0L) + 1L);
			}
			else if (NumberUtil.isBetween(value, new BigDecimal(2500), new BigDecimal(3169))) {
				b40.put(B_2_RM_2_500_TO_RM_3_169, b40.getOrDefault(B_2_RM_2_500_TO_RM_3_169, 0L) + 1L);
			}
			else if (NumberUtil.isBetween(value, new BigDecimal(3170), new BigDecimal(3969))) {
				b40.put(B_3_RM_3_170_TO_RM_3_969, b40.getOrDefault(B_3_RM_3_170_TO_RM_3_969, 0L) + 1L);
			}
			else if (NumberUtil.isBetween(value, new BigDecimal(3970), new BigDecimal(4849))) {
				b40.put(B_4_RM_3_970_TO_RM_4_849, b40.getOrDefault(B_4_RM_3_970_TO_RM_4_849, 0L) + 1L);
			}

			// m40
			else if (NumberUtil.isBetween(value, new BigDecimal(4850), new BigDecimal(5879))) {
				m40.put(M_1_TO_RM_4_850_TO_RM_5879, m40.getOrDefault(M_1_TO_RM_4_850_TO_RM_5879, 0L) + 1L);
			}
			else if (NumberUtil.isBetween(value, new BigDecimal(5880), new BigDecimal(7099))) {
				m40.put(M_2_RM_5_880_TO_RM_7_099, m40.getOrDefault(M_2_RM_5_880_TO_RM_7_099, 0L) + 1L);
			}
			else if (NumberUtil.isBetween(value, new BigDecimal(7100), new BigDecimal(8699))) {
				m40.put(M_3_RM_7_100_TO_RM_8_699, m40.getOrDefault(M_3_RM_7_100_TO_RM_8_699, 0L) + 1L);
			}
			else if (NumberUtil.isBetween(value, new BigDecimal(8700), new BigDecimal(10959))) {
				m40.put(M_4_RM_8_700_TO_RM_10_959, m40.getOrDefault(M_4_RM_8_700_TO_RM_10_959, 0L) + 1L);
			}

			// t20
			else if (NumberUtil.isBetween(value, new BigDecimal(10960), new BigDecimal(15039))) {
				t20.put(T_1_RM_10_960_T_0_RM_15_039, t20.getOrDefault(T_1_RM_10_960_T_0_RM_15_039, 0L) + 1L);
			}
			else {
				t20.put(T_2_MORE_THAN_RM_15_039, t20.getOrDefault(T_2_MORE_THAN_RM_15_039, 0L) + 1L);
			}
		});

		result.put("B40", b40);
		result.put("M40", m40);
		result.put("T20", t20);
		return result;
	}

	@Override
	public BigDecimal findLastByUserAndIncomeType(String userId, String incomeTypeId) {
		Income income = incomeService.findLastByUserAndIncomeType(userId, incomeTypeId);
		if (income == null) {
			return null;
		}
		return income.getMonthlyIncomeAmount();
	}

	@Override
	public List<IncomeDTO> getUserCurrentIncomes() {
		String refNo = SecurityUtil.currentUserLogin();
		return incomeService.getUserCurrentIncome(refNo);
	}

	@Override
	public void publishNetWorthTransactionEvent(String userId, NetWorthMainType mainType, NetWorthType subType,
			NetWorthSource source, BigDecimal incomeAmount) {

		UpdateUserNetWorthTransactionRequest request = UpdateUserNetWorthTransactionRequest.builder().userId(userId)
				.mainType(mainType).subType(subType).amount(incomeAmount).source(source).build();

		kafkaSender.safeSendWrapped(KafkaTopic.UPDATE_USER_NET_WORTH_TRANSACTION_TOPIC, userId, request);

	}

}
