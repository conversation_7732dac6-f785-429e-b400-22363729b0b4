package my.com.mandrill.component.service;

import my.com.mandrill.component.constant.NetWorthMainType;
import my.com.mandrill.component.constant.NetWorthSource;
import my.com.mandrill.component.constant.NetWorthType;
import my.com.mandrill.component.domain.Income;
import my.com.mandrill.component.domain.User;
import my.com.mandrill.component.dto.model.IncomeDTO;
import my.com.mandrill.component.dto.request.ReminderIntegrationRequest;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;

public interface IncomeIntegrationService {

	void processReminder(Income income, Boolean isReminder, ReminderIntegrationRequest reminderIntegrationRequest);

	void deleteReminder(Income income);

	void delete(String id, User user);

	Map<String, Map<String, Long>> countIncomeGroup(LocalDate startDate, String timeZone);

	BigDecimal findLastByUserAndIncomeType(String userId, String incomeTypeId);

	List<IncomeDTO> getUserCurrentIncomes();

	void publishNetWorthTransactionEvent(String userId, NetWorthMainType mainType, NetWorthType subType,
			NetWorthSource source, BigDecimal incomeAmount);

}
